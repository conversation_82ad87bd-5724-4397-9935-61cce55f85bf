using System;
using System.Collections.Generic;
using External;
using Google.Protobuf;

namespace Client.Common.Network.ExternalEvents.EventReceivers
{
    public class ExternalEventReceivers
    {
        private readonly Dictionary<ExternalEventType, IExternalEventReceiver> _receivers;

        public ExternalEventReceivers()
        {
            _receivers = new Dictionary<ExternalEventType, IExternalEventReceiver>
            {
                {ExternalEventType.EventTypeChangeMainBalance, new ChangeMainBalanceReceiver()},
                {ExternalEventType.EventTypeChangeRankInfo, new ChangeRankInfoReceiver()},
                {ExternalEventType.EventTypeChangeBloodInfo, new ChangeBloodInfoReceiver()},
                {ExternalEventType.EventTypeChangeFtueInfo, new ChangeFtueInfoReceiver()},
                {ExternalEventType.EventTypeChangePocketsInfo, new ChangePocketsInfoReceiver()},
                {ExternalEventType.EventTypeChangeEquipmentInfo, new ChangeEquipmentInfoReceiver()},
                {ExternalEventType.EventTypeChangeBloodBottleInfo, new ChangeBloodBottleInfoReceiver()},
                {ExternalEventType.EventTypeChangeTimerInfo, new ChangeTimerInfoReceiver()},
            };
        }


        public IExternalEventReceiver Get(ExternalEventType eventType)
        {
            return _receivers[eventType];
        }

        public IExternalEventReceiver<TReceiver> Get<TReceiver>(ExternalEventType eventType)
            where TReceiver : class, IMessage
        {
            return _receivers[eventType] as IExternalEventReceiver<TReceiver>;
        }

        public IEnumerable<IExternalEventReceiver> GetAll()
        {
            return _receivers.Values;
        }
    }

    public class ChangeMainBalanceReceiver : IExternalEventReceiver<ChangeMainBalance>
    {
         public event Action<ChangeMainBalance> Received;

         public void Receive(ExternalEvent externalEvent)
         {
            ChangeMainBalance data = ChangeMainBalance.Parser.ParseFrom(externalEvent.Data);
            Received?.Invoke(data);
         }
     }

    public class ChangeRankInfoReceiver : IExternalEventReceiver<ChangeRankInfo>
    {
         public event Action<ChangeRankInfo> Received;

         public void Receive(ExternalEvent externalEvent)
         {
            ChangeRankInfo data = ChangeRankInfo.Parser.ParseFrom(externalEvent.Data);
            Received?.Invoke(data);
         }
     }

    public class ChangeBloodInfoReceiver : IExternalEventReceiver<ChangeBloodInfo>
    {
         public event Action<ChangeBloodInfo> Received;

         public void Receive(ExternalEvent externalEvent)
         {
            ChangeBloodInfo data = ChangeBloodInfo.Parser.ParseFrom(externalEvent.Data);
            Received?.Invoke(data);
         }
     }

    public class ChangeFtueInfoReceiver : IExternalEventReceiver<ChangeFtueInfo>
    {
         public event Action<ChangeFtueInfo> Received;

         public void Receive(ExternalEvent externalEvent)
         {
            ChangeFtueInfo data = ChangeFtueInfo.Parser.ParseFrom(externalEvent.Data);
            Received?.Invoke(data);
         }
     }

    public class ChangePocketsInfoReceiver : IExternalEventReceiver<ChangePocketsInfo>
    {
         public event Action<ChangePocketsInfo> Received;

         public void Receive(ExternalEvent externalEvent)
         {
            ChangePocketsInfo data = ChangePocketsInfo.Parser.ParseFrom(externalEvent.Data);
            Received?.Invoke(data);
         }
     }

    public class ChangeEquipmentInfoReceiver : IExternalEventReceiver<ChangeEquipmentInfo>
    {
         public event Action<ChangeEquipmentInfo> Received;

         public void Receive(ExternalEvent externalEvent)
         {
            ChangeEquipmentInfo data = ChangeEquipmentInfo.Parser.ParseFrom(externalEvent.Data);
            Received?.Invoke(data);
         }
     }

    public class ChangeBloodBottleInfoReceiver : IExternalEventReceiver<ChangeBloodBottleInfo>
    {
         public event Action<ChangeBloodBottleInfo> Received;

         public void Receive(ExternalEvent externalEvent)
         {
            ChangeBloodBottleInfo data = ChangeBloodBottleInfo.Parser.ParseFrom(externalEvent.Data);
            Received?.Invoke(data);
         }
     }

    public class ChangeTimerInfoReceiver : IExternalEventReceiver<ChangeTimerInfo>
    {
         public event Action<ChangeTimerInfo> Received;

         public void Receive(ExternalEvent externalEvent)
         {
            ChangeTimerInfo data = ChangeTimerInfo.Parser.ParseFrom(externalEvent.Data);
            Received?.Invoke(data);
         }
     }


}