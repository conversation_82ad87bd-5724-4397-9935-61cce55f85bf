using System.Collections.Generic;
using System.Threading;
using Client.Common.AppVersion;
using Cysharp.Threading.Tasks;
using Newtonsoft.Json;

namespace Client.Common.Network.Http
{
    public enum Hosts
    {
        Development,
        Production,
    }
    
    public static class HttpNet
    {
        public const string SERVICE_UNAVAILABLE_ERROR = "503";

        public const string DEVELOPMENT_HOST = "https://hd.billions.app";
        public const string PRODUCTION_HOST = "https://hp.billions.app";
        public const string LOCAL_HOST = "http://localhost:3000";
        public const string WEBDAV_HOST = "https://webdav.billions.app";

        public static string Host;

        static HttpNet()
        {
            Host = HttpNetHost.GetHost();
        }
        
        public static async UniTask ApplyServerType(CancellationToken cancellationToken)
        {
            string serverTypeHost = await GetServerTypeData(cancellationToken);
            if (string.IsNullOrEmpty(serverTypeHost))
            {
                return;
            }

            Host = HttpNetHost.GetServerHost(serverTypeHost);
        }

        private static async UniTask<string> GetServerTypeData(CancellationToken cancellationToken)
        {
            (string response, string err) data = await HttpRequestHelper.GetServerInfo(cancellationToken);
            if (!string.IsNullOrEmpty(data.err))
            {
                return null;
            }

            string serverType = await GetVersion(data.response, cancellationToken);
            return string.IsNullOrEmpty(serverType) ? null : serverType;
        }

        private static async UniTask<string> GetVersion(string response, CancellationToken cancellationToken)
        {
            Dictionary<string, string> data = JsonConvert.DeserializeObject<Dictionary<string, string>>(response);
            AppVersion.AppVersion clientVersion = await AppVersionProvider.GetClientVersion(cancellationToken);
            foreach (KeyValuePair<string, string> serverData in data)
            {
                if (serverData.Key != clientVersion.ProductionVersion)
                {
                    continue;
                }

                return serverData.Value;
            }

            return null;
        }
    }
}