using System;
using Client.Common.ConfigHelper.Implementations;
using Client.Common.Network.MetaNet;
using Client.Common.TimeGiver.Abstractions;
using Client.Common.TimeGiver.Implementations;
using Client.Utils.ServiceTool;

namespace Client.Common.Battle.WinRate
{
    public class WinRateDataController
    {
        private readonly WinRateDataStorage _winRateDataStorage;
        private readonly ITimeGiver _timeGiver;
        private readonly BattleResult _battleResult;
        private readonly PlayerPrefsConfigHelper<WinRateDataStorage> _winRateDataStorageConfig;
        
        public WinRateDataController(BattleResult battleResult = new())
        {
            _winRateDataStorageConfig = new PlayerPrefsConfigHelper<WinRateDataStorage>(LocalIdents.Path.WIN_RATE_DATA_STORAGE_PATH);

            if (!_winRateDataStorageConfig.TryLoad(out _winRateDataStorage))
            {
                _winRateDataStorage = new WinRateDataStorage();
            }
            
            _timeGiver = Service<ServerTimeService>.Get();
            _battleResult = battleResult;
        }

        public void SetWinRateData()
        {
            CheckStreakLosesCounter();

            CheckLastSeenData();
            
            _winRateDataStorageConfig.Save(_winRateDataStorage);
        }

        private void CheckLastSeenData()
        {
            if (!CheckLastSeenTime())
            {
               _winRateDataStorage.LastSeenWinRateDate = _timeGiver.UtcNow;
                return;
            }
            
            if (CheckLastSeenCounter())
            {
                _winRateDataStorage.LastSeenWinRateCounter++;
            }
            else
            {
                _winRateDataStorage.LastSeenWinRateCounter = 0;
                _winRateDataStorage.LastSeenWinRateDate = _timeGiver.UtcNow;    
            }
        }

        private void CheckStreakLosesCounter()
        {
            if (_battleResult == BattleResult.Win)
            {
                _winRateDataStorage.LosesCounterWinRate = 0;
            }
            else
            {
                _winRateDataStorage.LosesCounterWinRate++;
            }
        }

        public bool CheckLastSeen() => CheckLastSeenTime() || CheckLastSeenCounter();
        private bool CheckLastSeenTime() => _timeGiver.UtcNow <= _winRateDataStorage.LastSeenWinRateDate.AddDays(3);
        private bool CheckLastSeenCounter() => _winRateDataStorage.LastSeenWinRateCounter < LocalIdents.MmrBot.BORDER_FIGHTS_MMR;
        public bool CheckLosesCounter() => _winRateDataStorage.LosesCounterWinRate < LocalIdents.MmrBot.BORDER_FIGHTS_MMR;
    }
}