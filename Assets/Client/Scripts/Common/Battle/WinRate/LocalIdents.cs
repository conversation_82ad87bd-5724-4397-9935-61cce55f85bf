namespace Client.Common.Battle.WinRate
{
    public static class LocalIdents
    {
        public static class Mmr
        {
            public const int WIN_RATE_BORDER_PERCENT_GRADE0 = 50;
            public const int WIN_RATE_BORDER_PERCENT_GRADE1 = 60;
            public const int WIN_RATE_BORDER_PERCENT_GRADE2 = 70;
            public const int WIN_RATE_BORDER_PERCENT_GRADE3 = 71;
            public const int BORDER_FIGHTS_MMR = 15;
        }
        
        public static class MmrBot
        {
            public const int WIN_RATE_BORDER_PERCENT_GRADE0 = 40;
            public const int WIN_RATE_BORDER_PERCENT_GRADE1 = 45;
            public const int WIN_RATE_BORDER_PERCENT_GRADE2 = 49;
            public const int WIN_RATE_BORDER_PERCENT_GRADE3 = 54;
            public const int WIN_RATE_BORDER_PERCENT_GRADE4 = 59;
            public const int WIN_RATE_BORDER_PERCENT_GRADE5 = 60;
            public const int BORDER_FIGHTS_MMR = 3;
        }

        public static class Path
        {
            public const string WIN_RATE_DATA_STORAGE_PATH = "winrate.data.storage";
        }
    }
}