using Client.Common.CSV;
using Client.Common.Player.Controllers;
using UnityEngine;

namespace Client.Common.Battle.WinRate
{
    public class WinRateHelper
    {
        private readonly PlayerProgress _playerProgress;
        private readonly GoogleDocsData _googleDocsData;
        private readonly WinRateDataController _winRateDataStorage;

        public WinRateHelper(PlayerProgress playerProgress, GoogleDocsData googleDocsData)
        {
            _playerProgress = playerProgress;
            _googleDocsData = googleDocsData;
            _winRateDataStorage = new WinRateDataController();
        }

        public int GetWinRateType()
        {
            int winRate = GetWinRateGrade();

            return (int)GetWinRateIndex(winRate);
        }

        private int GetWinRateGrade()
        {
            int mmrBorder = (int)_googleDocsData.GetNumber("bot.mmr.winrate.border", LocalIdents.Mmr.BORDER_FIGHTS_MMR);
            int winRate = LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE0;

            if (_playerProgress.TotalBattles() > mmrBorder)
            {
                winRate = Mathf.Clamp(GetWinRate(), LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE0, LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE3);
            }

            return winRate;
        }

        public int GetWinRate()
        {
            float winRateCalc = (float)_playerProgress.Kills / (float)_playerProgress.TotalBattles();

            return (int)Mathf.Ceil(100 * winRateCalc);
        }

        private WinRateGrades GetWinRateIndex(int winRate)
        {
            return winRate switch
            {
                <= LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE0 => WinRateGrades.Grade0,
                <= LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE1 => WinRateGrades.Grade1,
                <= LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE2 => WinRateGrades.Grade2,
                >= LocalIdents.Mmr.WIN_RATE_BORDER_PERCENT_GRADE3 => WinRateGrades.Grade3
            };
        }

        public WinRateBotsGrades GetWinRateBotType()
        {
            int winRate = GetWinRateBotGrade();

            return GetWinRateBotIndex(winRate);
        }

        private int GetWinRateBotGrade()
        {
            int winRate = LocalIdents.MmrBot.WIN_RATE_BORDER_PERCENT_GRADE0;

            if (_playerProgress.TotalBattles() > LocalIdents.MmrBot.BORDER_FIGHTS_MMR
                && _winRateDataStorage.CheckLosesCounter()
                && _winRateDataStorage.CheckLastSeen())
            {
                winRate = Mathf.Clamp(GetWinRate(), LocalIdents.MmrBot.WIN_RATE_BORDER_PERCENT_GRADE0, LocalIdents.MmrBot.WIN_RATE_BORDER_PERCENT_GRADE5);
            }

            return winRate;
        }

        private WinRateBotsGrades GetWinRateBotIndex(int winRate)
        {
            return winRate switch
            {
                <= LocalIdents.MmrBot.WIN_RATE_BORDER_PERCENT_GRADE0 => WinRateBotsGrades.Grade0,
                <= LocalIdents.MmrBot.WIN_RATE_BORDER_PERCENT_GRADE1 => WinRateBotsGrades.Grade1,
                <= LocalIdents.MmrBot.WIN_RATE_BORDER_PERCENT_GRADE2 => WinRateBotsGrades.Grade2,
                <= LocalIdents.MmrBot.WIN_RATE_BORDER_PERCENT_GRADE3 => WinRateBotsGrades.Grade3,
                <= LocalIdents.MmrBot.WIN_RATE_BORDER_PERCENT_GRADE4 => WinRateBotsGrades.Grade4,
                >= LocalIdents.MmrBot.WIN_RATE_BORDER_PERCENT_GRADE5 => WinRateBotsGrades.Grade5,
            };
        }
    }
}