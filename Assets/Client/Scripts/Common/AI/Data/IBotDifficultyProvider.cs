using System.Threading;
using Client.Common.Battle.WinRate;
using Client.Utils.ResultTool.Results;
using Cysharp.Threading.Tasks;

namespace Client.Common.AI.Data
{
    public interface IBotDifficultyProvider
    {
        public UniTask<Result> Init(WinRateBotsGrades winRate, CancellationToken cancellationToken);
        public float GetValue(string parameter, int rank, float defaultValue = 0f);
    }
}