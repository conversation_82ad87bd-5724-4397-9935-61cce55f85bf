using System.Threading;
using Client.Common.AI.Data;
using Client.Common.Battle.WinRate;
using Client.Common.Player.Controllers;
using Client.Utils.ResultTool.Results;
using Cysharp.Threading.Tasks;

namespace Client.Common.AI.Givers
{
    public class BotDifficultyProvider : IBotDifficultyProvider
    {
        private readonly BotDifficultyData _botDifficultyData;
        private readonly BotDifficultyMmrGiver _botDifficultyMmrGiver;
        private readonly PlayerWinStreak _playerWinStreak;

        private int _winStreak;
        private int _winRate;

        public BotDifficultyProvider(BotDifficultyData botDifficultyData, BotDifficultyMmrGiver botDifficultyMmrGiver, PlayerWinStreak playerWinStreak)
        {
            _botDifficultyData = botDifficultyData;
            _botDifficultyMmrGiver = botDifficultyMmrGiver;
            _playerWinStreak = playerWinStreak;
        }

        public async UniTask<Result> Init(WinRateBotsGrades winRate, CancellationToken cancellationToken)
        {
            _winRate = (int)winRate;
            Result<int> result = await _playerWinStreak.GetWinStreak(cancellationToken);
            if (result.IsFailure)
            {
                return Result.Failure(result.Error);
            }
            _winStreak = result.Value;
            return Result.Success();
        }
        
        public float GetValue(string parameter, int rank, float defaultValue = 0)
        {
            return _botDifficultyData.GetValue(parameter, _winRate, GetAiSubType(rank));
        }

        private AiSubType GetAiSubType(int rank)
        {
            return _botDifficultyMmrGiver.GetDifficultyLevel(rank, _winStreak);
        }
    }
}