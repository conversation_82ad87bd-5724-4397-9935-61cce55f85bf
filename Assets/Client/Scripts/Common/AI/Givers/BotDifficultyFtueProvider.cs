using System.Threading;
using Client.Common.AI.Data;
using Client.Common.Battle.WinRate;
using Client.Utils.ResultTool.Results;
using Cysharp.Threading.Tasks;

namespace Client.Common.AI.Givers
{
    public class BotDifficultyFtueProvider : IBotDifficultyProvider
    {
        private readonly BotDifficultyData _botDifficultyData;

        private int _winStreak;

        public BotDifficultyFtueProvider(BotDifficultyData botDifficultyData)
        {
            _botDifficultyData = botDifficultyData;
        }

        public UniTask<Result> Init(WinRateBotsGrades winRate, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(Result.Success());
        }
        
        public float GetValue(string parameter, int rank, float defaultValue = 0)
        {
            return _botDifficultyData.GetValue(parameter, rank, AiSubType.Easy);
        }
    }
}