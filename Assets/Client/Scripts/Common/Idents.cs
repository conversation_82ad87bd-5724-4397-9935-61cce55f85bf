namespace Client.Common
{
    public static class Idents
    {
        public static class Scenes
        {
            public const string AltarTournament = "AltarTournament";
            public const string AltarTournamentAwards = "AltarTournamentAwards";
            public const string AltarTournamentResult = "AltarTournamentResult";
            public const string AltarGlobals = "AltarGlobals";
            public const string ACHIEVEMENTS = "Achievements";
            public const string Alchemist = "Alchemist";
            public const string Bank = "Bank";
            public const string Battle = "Battle";
            public const string BattleResult = "BattleResult";
            public const string Blacksmith = "Blacksmith";
            public const string Connection = "Connection";
            public const string Intro = "Intro";
            public const string Init = "Init";
            public const string Junkman = "Junkman";
            public const string Leagues = "LeaguesLeaderboard";
            public const string Map = "Map";
            public const string Loadout = "Loadout";
            public const string Market = "Market";
            public const string AltarRace = "AltarRace";
            public const string PROFILE_HOME = "ProfileHome";
            public const string ProfileShurikens = "ProfileShurikens";
            public const string ProfilePowerStones = "ProfilePowerStones";
            public const string ProfileHooks = "ProfileHooks";
            public const string ProfileBombs = "ProfileBombs";
            public const string ProfileAmulets = "ProfileAmulets";
            public const string ProfileProgress = "ProfileProgress";
            public const string ProfileEditName = "ProfileEditName";
            public const string ProfileMasks = "ProfileMasks";
            public const string ProfileOutfits = "ProfileOutfits";
            public const string Region = "Region";
            public const string SessionResult = "SessionResult";
            public const string Settings = "GameSettings";
            public const string Teahouse = "Teahouse";
            public const string Wheel = "Wheel";
            public const string Prizes = "Prizes";
            public const string VERSION_VALIDATION = "VersionValidation";
            public const string DEBUG = "Debug";
        }

        public static class Layers
        {
            public const string Ui = "UI";
        }

        public static class Tags
        {
            public const string MainCamera = "MainCamera";
            public const string UiCamera = "UiCamera";
            public const string MAIN_CANVAS_SAFE_ZONE = "MainCanvasSafeZone";
            public const string Player = "Player";
        }

        public static class Ranks
        {
            public const int RANKS_COUNT = 14;
            public const int RANKS_BOT_COUNT = 21;
            public const int RANKS_MMR_COUNT = 3;
        }

        public static class LocalConfigKeys
        {
            public const string USER_STATS = "LocalUserStats";
        }
        
        public static class ShieldHints
        {
            public const string COOLDOWN = "hint.battle.shield.cooldown";
            public const string SHIELD_CRITICAL_VALUE = "hint.battle.shield.criticalShieldValue";
            public const string HINT_LIFETIME = "hint.battle.shield.lifeTime";
        }

        public static class ReferenceResolution
        {
            public const int X = 750;
            public const int Y = 1334;
        }
    }
}