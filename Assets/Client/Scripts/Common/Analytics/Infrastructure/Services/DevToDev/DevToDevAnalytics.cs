using System.Collections.Generic;
using System.Threading;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.GameSettings;
using Client.Common.Network.Http;
using Cysharp.Threading.Tasks;
using DevToDev.Analytics;
using Newtonsoft.Json;

namespace Client.Common.Analytics.Infrastructure.Services.DevToDev
{
    public class DevToDevAnalytics : BaseAnalyticsSystemWrapper
    {
        private struct ServerTestersResponse
        {
            public string TestersDevices;
        }
        
        protected override IAnalyticsFilter EventFilter { get; } = new PassAnalyticsFilter();

        public override void SetUserData(string userId, bool tgLink = false, CancellationToken cancellationToken = default)
        {
            DTDAnalytics.SetUserId(userId);
            SetTesterData(cancellationToken);
            SetTgLink(tgLink);
        }

        private async void SetTesterData(CancellationToken cancellationToken = default)
        {
#if DEBUG
            DTDUserCard.SetTester(true);
#endif
#if PRODUCTION
            if (HttpNet.Host == HttpNet.DEVELOPMENT_HOST)
            {
                DTDUserCard.SetTester(true);
            }
            
            DTDUserCard.SetTester(await GetTestDevices(cancellationToken));
#endif
        }

        private async UniTask<bool> GetTestDevices(CancellationToken cancellationToken)
        {
            (string response, string err) data = await HttpRequestHelper.GetServerInfo(cancellationToken);

            if (!string.IsNullOrEmpty(data.err))
            {
                return false;
            }

            ServerTestersResponse dataRaw = JsonConvert.DeserializeObject<ServerTestersResponse>(data.response);

            if (dataRaw.TestersDevices == null)
            {
                return false;
            }
            
            return dataRaw.TestersDevices.Contains(DeviceData.DeviceId());
        }

        private void SetTgLink(bool tgLink)
        {
            FireLogEventWithParameter("telegram_link", new Dictionary<string, string>
            {
                {"HasTelegramLink",tgLink.ToString()}
            });
        }

        public override void Init()
        {
            IsInitialized = true;
        }

        protected override void FireLogEvent(object eventName)
        {
            DTDAnalytics.CustomEvent((string) eventName);
        }

        protected override void FireLogEventWithParameter(string eventName, Dictionary<string, string> eventParams)
        {
            DTDCustomEventParameters customEventParams = new();
            foreach (KeyValuePair<string, string> param in eventParams)
            {
                customEventParams.Add(param.Key, param.Value);
            }

            DTDAnalytics.CustomEvent(eventName, customEventParams);
        }

        public override void TrackPurchase(AnalyticsRealPurchase analyticsRealPurchase)
        {
            DTDAnalytics.RealCurrencyPayment(analyticsRealPurchase.ReceiptId, (float) analyticsRealPurchase.Revenue, analyticsRealPurchase.ContentId, analyticsRealPurchase.Currency);
        }

        public override void TrackLevelUp(int progress, Dictionary<string, long> parameters)
        {
            DTDAnalytics.LevelUp(progress, parameters);
        }

        public override void TrackRankChange(Dictionary<string, string> parameters)
        {
            RankChangeEvent(parameters);
        }

        public override void TrackTutorialStep(int step)
        {
            DTDAnalytics.Tutorial(step);
        }

        public override void TrackTutorialContextualStep(int step)
        {
            TutorialContextualStepEvent(step);
        }

        public override void TrackInApp(AnalyticsInAppPurchase purchase)
        {
            DTDAnalytics.VirtualCurrencyPayment(purchase.Id, purchase.PurchaseType, 1, GetPriceParameters(purchase));
        }

        public override void TrackNotificationTapped(string notificationsId)
        {
            NotificationTapedEvent(notificationsId);
        }

        public override void TrackReturnPlayer()
        {
            ReturnPlayerEvent();
        }

        public override void TrackSetNotificationInQueue(string notificationsId)
        {
            SetNotificationInQueueEvent(notificationsId);
        }
    }
}