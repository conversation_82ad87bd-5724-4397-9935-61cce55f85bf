using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Analytics.Infrastructure.Services.DevToDev;
using Client.Common.Analytics.Infrastructure.Services.Google;
using Client.Utils.Attributes;

namespace Client.Common.Analytics.Infrastructure.Services
{
    public class AnalyticsService : IAnalyticsSender {
        private readonly LoggerAnalytics _loggerAnalytics = new LoggerAnalytics ();
        private DevToDevAnalytics DevToDevAnalytics { get; } = new DevToDevAnalytics ();
        private FireBaseGoogleAnalytics FireBaseGoogleAnalytics { get; } = new FireBaseGoogleAnalytics ();

        readonly List<BaseAnalyticsSystemWrapper> _analyticsSystemWrappers;
        public bool IsInitialized => _analyticsSystemWrappers.All(wrapper => wrapper.IsInitialized);

        private readonly CancellationTokenSource _cancellationTokenSource = new CancellationTokenSource();

        public AnalyticsService()
        {
            _analyticsSystemWrappers = new List<BaseAnalyticsSystemWrapper> {
                _loggerAnalytics,
                DevToDevAnalytics,
                FireBaseGoogleAnalytics
            };
        }

        public void Init()
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.Init();
            }
        }
        
        public void SetUserId(string userId, bool tgLink = false)
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.SetUserData(userId, tgLink, _cancellationTokenSource.Token);
            }
        }

        public void LogEvent(Enum eventType)
        {
            LogEvent(eventType.GetStringValue());
        }

        public void LogEvent(string eventName)
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.LogEvent(eventName);
            }
        }

        public void LogEventWithParameters(string eventName, Dictionary<string, string> eventParams)
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.LogEventWithParameters(eventName, eventParams);
            }
        }

        public void TrackPurchase(AnalyticsRealPurchase analyticsRealPurchase) {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.TrackPurchase(analyticsRealPurchase);
            }
        }

        public void LogLevelUp(int progress, Dictionary<string, long> parameters)
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.TrackLevelUp(progress, parameters);
            }
        }
        
        public void LogRankChange(Dictionary<string, string> parameters)
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.TrackRankChange(parameters);
            }
        }

        public void TrackTutorialStep(int step)
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.TrackTutorialStep(step);
            }
        }

        public void TrackInApp(AnalyticsInAppPurchase purchase)
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.TrackInApp(purchase);
            }
        }
        
        public void TrackNotificationTapped(string notificationId)
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.TrackNotificationTapped(notificationId);
            }
        }
        
        public void TrackReturnPlayer()
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.TrackReturnPlayer();
            }
        }
        
        public void TrackSetNotificationInQueue(string notificationId)
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.TrackSetNotificationInQueue(notificationId);
            }
        }
        
        public void TrackTutorialContextualStep(int step)
        {
            foreach(BaseAnalyticsSystemWrapper baseAnalyticsSystemWrapper in _analyticsSystemWrappers)
            {
                baseAnalyticsSystemWrapper.TrackTutorialContextualStep(step);
            }
        }
    }
}