using Client.Common.Battle.WinRate;
using Client.Common.BlurTool.ScreenshotBlur;
using Client.Common.Components.Unit;
using Client.Common.CSV;
using Client.Common.LocalLogic.FPV;
using Client.Common.Player.Controllers;
using Client.Common.Player.Data;
using Client.Common.ScreenTransitions;
using Client.Common.ScreenTransitions.Views;
using Client.FPV.Battle.BattleResultScroll.BattleStatsScreen;
using Client.FPV.Battle.BattleResultScroll.BattleStatsScreen.Views;
using Client.FPV.Battle.Components;
using Client.FPV.Battle.Components.SoundEvents;
using Client.FPV.Battle.Systems.Analytics;
using Client.FPV.Battle.Systems.Stats;
using Client.TopDown.Region.Helpers;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ViewService;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using UnityEngine;
using Result = Client.Common.Network.MetaNet.BattleResult;

namespace Client.FPV.Battle.Systems.Ending
{
    public class VictoryDefeatSystem : IEcsRunSystem
    {
        private readonly ScreenshotBlurService _blur = default;
        private readonly LocalEcsWorld _world = default;
        private readonly TransitionService _transitionService = default;
        private readonly PopupViewService _viewService = default;
        private readonly PlayerManager _playerManager = default;
        private readonly BotHistoryGenerator _botHistoryGenerator = default;
        private readonly GoogleDocsData _googleDocsData = default;

        private readonly EcsFilter<BattleResultComponent, VictoryDefeatReadyEvent> _battleResult = default;
        private readonly EcsFilter<FPVUnit, UnitDeathAnimation, PlayerFlag> _playerAnimation = default;
        private readonly EcsFilter<FPVUnit, UnitDeathAnimation>.Exclude<PlayerFlag> _opponentAnimation = default;
        private readonly EcsFilter<FPVUnit, PlayerFlag> _player = default;
        private readonly EcsFilter<FPVUnit>.Exclude<PlayerFlag> _opponent = default;
        private readonly EcsFilter<BattleStatsCacheEvent> _battleStatsCacheFilter = default; 

        private readonly bool _tutorialBattle;
        
        private bool _battleEnded;

        public VictoryDefeatSystem(bool tutorial)
        {
            _tutorialBattle = tutorial;
        }

        public void Run()
        {
            if (_battleResult.IsEmpty() || _battleEnded)
            {
                return;
            }

            TryCloseBattleByPlayerDeath();
            TryCloseBattleByAllOpponentDeath();
        }

        private void TryCloseBattleByPlayerDeath()
        {
            if (_playerAnimation.IsEmpty())
            {
                return;
            }

            foreach (int index in _playerAnimation)
            {
                if (IsAnimationEnd(_playerAnimation.Get2(index)))
                {
                    CloseBattle(Result.Lose);

                    return;
                }
            }
        }

        private void TryCloseBattleByAllOpponentDeath()
        {
            if (!_playerAnimation.IsEmpty())
            {
                return;
            }

            foreach (int index in _opponentAnimation)
            {
                if (!IsAnimationEnd(_opponentAnimation.Get2(index)))
                {
                    return;
                }
            }

            CloseBattle(_battleResult.Get1(0).Result);
        }

        private bool IsAnimationEnd(UnitDeathAnimation deathAnimation)
        {
            return deathAnimation.EndTime < Time.time;
        }

        private void CloseBattle(Result result)
        {
            _battleEnded = true;

            _blur.Process(6);
            _world.NewEntity().Get<StopMusic>();

            if (_tutorialBattle)
            {
                ShowVictoryDefeatScreen(result).Forget();
            }
            else
            {
                ShowStatisticsScreen(result).Forget();
            }
        }

        private async UniTaskVoid ShowVictoryDefeatScreen(Result battleResult)
        {
            await UniTask.NextFrame();
            _transitionService.FadeIn(new VictoryDefeatTransitionView(Result.Win == battleResult, _blur), TransitOutOfBattle);

            void TransitOutOfBattle()
            {
                foreach (int index in _battleResult)
                {
                    _battleResult.GetEntity(index).Get<BattleResultTransition>();
                }
            }
        }

        private async UniTaskVoid ShowStatisticsScreen(Result battleResult)
        {
            PlayerSession session = _playerManager.Session;
            RivalBasicInfo playerInfo = session.GetInfo();
            BattleStatsPresenter statisticsScreen = await _viewService.Prepare<BattleStatsPresenter, BattleStatsView, BattleStatsModel>(
                new BattleStatsModel
                {
                    Result = battleResult,
                    PlayerEntity = _player.GetEntity(0),
                    OpponentEntity = _opponent.GetEntity(0),
                    PlayerThrows = session.BattleResult.PlayerShurikensThrownCount,
                    OpponentThrows = session.BattleResult.EnemyShurikensThrownCount,
                    Player = playerInfo,
                    Opponent = _botHistoryGenerator.Get(session.BattleResult, battleResult, session),
                    OldPoints = GetOldPoints(),
                    WinRate = new WinRateHelper(_playerManager.Session.Progress, _googleDocsData).GetWinRate(),
                    BattleId = session.LastBattleResultId,
                }, opener: new InstantOpener<BattleStatsView>(), closer: new InstantCloser<BattleStatsView>());

            await _transitionService.FadeIn();
            await statisticsScreen.Show();
            
            await _transitionService.FadeOut();
            await statisticsScreen.WaitClick();
            
            await _transitionService.FadeIn();
            await statisticsScreen.Hide();
            
            _battleResult.GetEntity(0).Get<BattleResultTransition>();
        }
        
        private int GetOldPoints()
        {
            int oldPoints = _battleStatsCacheFilter.Get1(0).BattlePoints;
            _battleStatsCacheFilter.GetEntity(0).Destroy();

            return oldPoints;
        }
    }
}