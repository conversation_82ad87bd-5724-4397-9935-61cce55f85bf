using System;
using System.Threading;
using Client.Common.AI.Data;
using Client.Common.Battle.WinRate;
using Client.Common.Components.Unit;
using Client.Common.HumanoidBody.Data;
using Client.Common.LocalLogic.FPV;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.FPV.Battle.Components;
using Client.FPV.Battle.Components.AI;
using Client.FPV.Battle.Data;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.Extensions;
using Client.Utils.ResultTool.Errors;
using Client.Utils.ResultTool.Results;
using Cysharp.Threading.Tasks;
using Leopotam.Ecs;
using BattleResult = Client.Common.Network.MetaNet.BattleResult;
using WeaponSkin = External.WeaponSkin;

namespace Client.FPV.Battle.Systems
{
    public struct BattleResultRequestsProcessed
    {
    }

    public class BattleResultServerRequestSystem : IEcsRunSystem, IEcsDestroySystem
    {
        private readonly LocalEcsWorld _world;
        private readonly PlayerManager _playerManager = default;
        private readonly MetaNet _metaNet = default;
        private readonly EcsFilter<FPVUnit, PlayerFlag> _player = default;
        private readonly EcsFilter<FPVUnit>.Exclude<PlayerFlag> _opponent = default;

        private readonly EcsFilter<BattleResultComponent>.Exclude<BattleResultRequestsProcessed> _battleResults;
        private readonly EcsFilter<BattleEnded> _battleEnd = default;

        private readonly CancellationTokenSource _cts = new();
        
        public void Run()
        {
            if (_battleEnd.IsEmpty())
            {
                return;
            }

            RunServerRequests(_cts.Token);
        }

        public void Destroy()
        {
            _cts?.CancelAndDispose();
        }

        private async void RunServerRequests(CancellationToken token)
        {
            foreach (int i in _battleResults)
            {
                BattleResultData battleResultData = new();
                BattleResult battleResult = _battleResults.Get1(i).Result;

                if (_playerManager.Session.RegionId > 0)
                {
                    Result<BattleResultData> result = await WaitForBattleResults(_playerManager.Session, _metaNet, token);

                    if (result.IsFailure)
                    {
                        _battleResults.GetEntity(i).Get<BattleResultRequestsProcessed>();
                        return;
                    }
                    
                    battleResultData = result.Value;
                }

                PlayerSession playerSession = _playerManager.Session;
                SaveSessionProgress(playerSession, battleResultData, playerSession.BattleResult);
                playerSession.BattleResultId = "";
                playerSession.RegionResultData.IsRevivedFromLastBattle = false;
                
                SetWinRateData(battleResult);

                _battleResults.GetEntity(i).Get<BattleResultRequestsProcessed>();
            }
        }

        private async UniTask<Result<BattleResultData>> WaitForBattleResults(PlayerSession playerSession, MetaNet metaNet, CancellationToken token)
        {
            Result<BattleResultData> failureResult = new Result<BattleResultData>(null, false, new Error(""));
            Result<ClientBattleResultResponse> clientBattleResult = await RequestBattleResult(playerSession.BattleResult, playerSession, metaNet, token);

            if (clientBattleResult.IsFailure)
            {
                return failureResult;
            }
            
            await SetShurikenSkinsDurabilities(playerSession.BattleResult, token);

            Result<BattleProgressResponse> battleProgress = await metaNet.GetBattleProgress(token);

            if (battleProgress.IsFailure)
            {
                return failureResult;
            }
            
            BattleResultData battleResultData = new()
            {
                ClientBattleResultResponse = clientBattleResult.Value,
                BattleProgressResponse = battleProgress.Value,
            };
            
            return new Result<BattleResultData>(battleResultData, true);
        }

        private async UniTask<Result<ClientBattleResultResponse>> RequestBattleResult(Common.Player.Controllers.BattleResult battleResult, PlayerSession session, MetaNet metaNet,
                                                                                      CancellationToken token)
        {
            BattleLocation battleLocation = new()
            {
                Region = (byte) session.RegionId,
                Point = (byte) session.LocationId
            };

            FPVUnit player = _player.Get1(0);
            EcsEntity opponentEntity = _opponent.GetEntity(0);
            UnitTakenHitsCount enemyHits = opponentEntity.Get<UnitTakenHitsCount>();
            BattleAITypeComponent battleAiType = opponentEntity.Get<BattleAITypeComponent>();
            UnitTakenHitsCount playerHits = player.Entity.Get<UnitTakenHitsCount>();

            BattleOpponent battleOpponent = new()
            {
                Rank = (ushort) battleResult.EnemyRank, Outfit = (ushort) battleResult.EnemyCloth,
                Country = battleResult.EnemyCountry, Shuriken = (ushort) battleResult.EnemyWeapon,
                Type = GetBattleAiType(battleAiType.AIType),
                Points = (ushort) _playerManager.Session.BattleResult.EnemyMmr,
                Injuries = new BattleOpponentInjuries
                {
                    Inflicted = (HitBodyPart) playerHits,
                    Received = (HitBodyPart) enemyHits
                },
                Damage = new BattleOpponentDamage
                {
                    Inflicted = player.DamageReceive,
                    Received = player.DamageApply
                }
            };


            Result<ClientBattleResultResponse> battleResultResponse = await metaNet.BattleResult(
                session.BattleResultId,
                session.RegionResultData.BattleResult,
                battleLocation,
                battleOpponent,
                battleResult.OutfitDurabilities,
                player.Health, token);

            return battleResultResponse;
        }

        private BattleOpponentType GetBattleAiType(BattleAIType battleAIType) => (BattleOpponentType) Enum.Parse(typeof(BattleOpponentType), battleAIType.ToString());

        private void SaveSessionProgress(PlayerSession session, BattleResultData battleResultData,
                                         Common.Player.Controllers.BattleResult battleResult)
        {
            battleResult.RankChangeDelta = battleResultData.ClientBattleResultResponse.Rank - session.Progress.Rank;

            session.Progress.UpdateBattleProgress(
                battleResultData.ClientBattleResultResponse.Rank,
                battleResultData.BattleProgressResponse.Points,
                battleResultData.BattleProgressResponse.Win,
                battleResultData.BattleProgressResponse.Lose);
        }

        private async UniTask SetShurikenSkinsDurabilities(Common.Player.Controllers.BattleResult battleResult, CancellationToken token = default)
        {
            WeaponSkin[] skinsDurabilitiesCopy = new WeaponSkin[battleResult.ShurikenSkinsDurabilities.Count];
            battleResult.ShurikenSkinsDurabilities.CopyTo(skinsDurabilitiesCopy);

            foreach (WeaponSkin weaponSkin in skinsDurabilitiesCopy)
            {
                await _metaNet.SetWeaponSkinsDurability(weaponSkin, token);
            }
        }

        private static void SetWinRateData(BattleResult battleResult)
        {
            WinRateDataController winRateDataStorage = new(battleResult);
            winRateDataStorage.SetWinRateData();
        }
    }
}