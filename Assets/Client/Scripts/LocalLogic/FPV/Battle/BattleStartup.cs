using System.Threading;
using System.Threading.Tasks;
using Client.Common.Abilities.Battle.Data.AbilityConfigs;
using Client.Common.Abilities.Infrastructure.Cooldown;
using Client.Common.AI.BehaviourTree.Runtime.NodeSystems;
using Client.Common.AI.BehaviourTree.Runtime.Scripts.Systems;
using Client.Common.AI.Data;
using Client.Common.Analytics.Helpers;
using Client.Common.Battle.WinRate;
using Client.Common.Configs.Components.Effects;
using Client.Common.CSV;
using Client.Common.Currency;
using Client.Common.Dialogs.Runtime.Systems;
using Client.Common.ECS.LocalWorld;
using Client.Common.Effects.Activation;
using Client.Common.Effects.Deactivation;
using Client.Common.Effects.Lifecycle;
using Client.Common.Effects.Lifecycle.Removing;
using Client.Common.Ftue.HUD;
using Client.Common.Ftue.Infrastructure;
using Client.Common.HintSystem;
using Client.Common.Network.Connection.ConnectionStatus;
using Client.Common.Network.Connection.Disconnect;
using Client.Common.Player.Controllers;
using Client.Common.ResourcesTopPanel.Systems;
using Client.Common.SceneLoading;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Common.ScreenTransitions;
using Client.Common.ScreenTransitions.Views.VSScreen;
using Client.Common.TriNavMeshes;
using Client.Common.UI.Labels;
using Client.FPV.Battle.Abilities.Amulet;
using Client.FPV.Battle.Abilities.Shuriken;
using Client.FPV.Battle.Abilities.Systems;
using Client.FPV.Battle.Abilities.Systems.Ability;
using Client.FPV.Battle.Abilities.Systems.Visual;
using Client.FPV.Battle.Ai.AdaptiveBehaviour;
using Client.FPV.Battle.Ai.ChargedThrowAccuracy;
using Client.FPV.Battle.Camera.Systems;
using Client.FPV.Battle.Camera.UnityComponents;
using Client.FPV.Battle.ChargedThrow;
using Client.FPV.Battle.ChargedThrow.Ability.Process;
using Client.FPV.Battle.Components;
#if DEBUG_PANEL
using Client.FPV.Battle.DebugTools.Stamina;
#endif
using Client.FPV.Battle.Durability;
using Client.FPV.Battle.Effects;
using Client.FPV.Battle.Effects.Activation;
using Client.FPV.Battle.Effects.Ai;
using Client.FPV.Battle.Effects.AiChangeShieldUsage;
using Client.FPV.Battle.Effects.AiChangeThrowAccuracy;
using Client.FPV.Battle.Effects.AiChangeThrowFrequency;
using Client.FPV.Battle.Effects.AutoHeal;
using Client.FPV.Battle.Effects.Bleeding;
using Client.FPV.Battle.Effects.BleedingModificator;
using Client.FPV.Battle.Effects.ChangeDamageAmount;
using Client.FPV.Battle.Effects.ChangeStaminaRecoverySpeed;
using Client.FPV.Battle.Effects.ChangeThrowCost;
using Client.FPV.Battle.Effects.EffectIcons;
using Client.FPV.Battle.Effects.FreezeUnit;
using Client.FPV.Battle.Effects.HealthDepletion;
using Client.FPV.Battle.Effects.InfiniteStamina;
using Client.FPV.Battle.Effects.ShieldDepletion;
using Client.FPV.Battle.Effects.ShieldPiercing;
using Client.FPV.Battle.Effects.SpeedupMovement;
using Client.FPV.Battle.Effects.StaminaDepletion;
using Client.FPV.Battle.Effects.StaminaUsageSkip;
using Client.FPV.Battle.Hints.HintShield;
using Client.FPV.Battle.HUD;
using Client.FPV.Battle.Movement;
using Client.FPV.Battle.Network.Systems;
using Client.FPV.Battle.PlayerBars;
using Client.FPV.Battle.PlayerBars.Systems;
using Client.FPV.Battle.QTE;
using Client.FPV.Battle.QTE.MainScreen;
using Client.FPV.Battle.Quests;
using Client.FPV.Battle.ShieldBehaviour.Defence;
using Client.FPV.Battle.ShieldBehaviour.Defence.Views;
using Client.FPV.Battle.Stamina;
using Client.FPV.Battle.Systems;
using Client.FPV.Battle.Systems.AI;
using Client.FPV.Battle.Systems.AI.Movement;
using Client.FPV.Battle.Systems.AI.ShieldBehaviour;
using Client.FPV.Battle.Systems.AI.Throw;
using Client.FPV.Battle.Systems.Analytics;
using Client.FPV.Battle.Systems.BattleConditions.Systems;
using Client.FPV.Battle.Systems.BattleTimer;
using Client.FPV.Battle.Systems.ConditionSelection;
using Client.FPV.Battle.Systems.ConditionSelection.Systems;
using Client.FPV.Battle.Systems.Ending;
using Client.FPV.Battle.Systems.Events;
using Client.FPV.Battle.Systems.Ftue;
using Client.FPV.Battle.Systems.Shurikens;
using Client.FPV.Battle.Systems.Stats;
using Client.FPV.Battle.Systems.UI;
using Client.FPV.Battle.Systems.Unit;
using Client.FPV.Battle.Systems.UserHand;
using Client.FPV.Battle.Systems.UserInputs;
using Client.FPV.Battle.Systems.Wounds;
using Client.FPV.Battle.UnityComponents;
using Client.FPV.Battle.VsScreen;
using Client.TopDown.Region.Helpers;
using Client.Utils.ECS.ECSObservableSystem;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Client.Utils.Visible;
using Leopotam.Ecs;
using NaughtyAttributes;
using UnityEngine;
using VContainer;
using BattleResultTransitionSystem = Client.FPV.Battle.Systems.Ending.BattleResultTransitionSystem;

namespace Client.FPV.Battle
{
    internal struct BattleScope
    {
    }

    internal sealed class BattleStartup : LocalWorld<BattleScope>
    {
        [SerializeField] private BattleLifeTimeScope _lifeTimeScope;
        
        [SerializeField] private BattleCommonVisual _commonVisual;
        [SerializeField] private BattleEffectUiBundle _effectUiBundle;
        
        [HorizontalLine()]
        [Header("Player hud")]
        [SerializeField] private PlayerBarView _staminaView;
        [SerializeField] private CooldownBarView _staminaCooldownView;
        [SerializeField] private PlayerBarView _healthView;
        [SerializeField] private ShieldBarView _shieldBarView;
        [SerializeField] private TextView _centerHintText;
        
        [Header("Arrows")]
        [SerializeField] private Transform _arrowsHolder;
        [SerializeField] private GameObject _leftArrow;
        [SerializeField] private GameObject _rightArrow;
        
        [Header("Player anchors")]
        [SerializeField] private Transform _userHandAnchor;
        [SerializeField] private Transform _playerRoot;
        [SerializeField] private UnitCameraComponent _unitCamera;
        
        [HorizontalLine()]
        [Header("Fullscreen")]
        [SerializeField] private Canvas _overlayCanvas;
        [SerializeField] private VSScreenView _vsScreenView;
        [SerializeField] private ReadyFightView _readyFightView;
        [SerializeField] private QteMainHudView _qteMainHUDView;
        [SerializeField] private HUDView _hudView;

        [Header("Shurikens")]
        [SerializeField] private Transform _trailVfxPoolRoot;

        [Inject] private SceneLoader _sceneLoader;
        [Inject] private PlayerManager _playerManager;
        [Inject] private IBotDifficultyProvider _botDifficultyProvider;
        [Inject] private VisibilityController _hudVisibilityController;
        [Inject] private LocalAnalyticsHelper _analyticsHelper;
        [Inject] private GoogleDocsData _googleDocsData;
        [Inject] private FtueProgress _ftueProgress;
        [Inject] private TriNavMesh _navMesh;
        
        protected override async Task OnStart(CancellationToken cancellationToken)
        {
            _hudVisibilityController.Hide();
            
            BattleFeatureConfig featureConfig = _sceneLoader.GetCurrent<BattleSceneLoadData>().FeatureConfig;

            WinRateHelper winRateHelper = new(_playerManager.Session.Progress, _googleDocsData);
            WinRateBotsGrades winRateBot = winRateHelper.GetWinRateBotType();
            await _botDifficultyProvider.Init(winRateBot, cancellationToken);

            World.NewEntity().Get<Statistics>();

            if (!featureConfig.Ftue)
            {
                Systems
                    .AddFeature(new BattleQuestFeature());
            }
                
            Systems
                .AddFeature(new HintSystemFeature(World))
                .Add(new CameraInitSystem())
                .Add(new EnvironmentInitSystem())
                .Add(new UnitInitSystem(_playerRoot.gameObject, _botDifficultyProvider))
                .AddFeature(new BehaviourTreeFeature(_botDifficultyProvider))
                .Add(new NewBattleAiBehaviourTreeSystem())
                .Add(new BattleStartEffectsActivationSystem());

            //start battle
            Systems.Add(new VSScreenDisconnectSystem(featureConfig))
                   .Add(new ConditionInjectedUiDisableSystem());
            if (featureConfig.Conditions)
            {
                Systems.AddFeature(new ConditionFeature(_effectUiBundle));
            }
            if (featureConfig.Ftue)
            {
                Systems.Add(new FtueHUDStateSystem())
                       .AddFeature(new FtueFirstBattleFeature())
                       .Add(new FtuePart2System())
                       .Add(new FtuePart3System());
            }
            if (featureConfig.Ftue && featureConfig.Parrying)
            {
                Systems.Add(new FtueParrySystem());
            }

            Systems.Add(new BattleFreezeControlSystem())
                   .Add(new ReadyFightSystem(_hudVisibilityController, _readyFightView))

                   //input
                   .AddFeature(new TouchFeature())
                   .Add(new UserThrowShurikenSystem(_unitCamera.PlayerBodyTransform))
                   .Add(new UserThrowFollowupSystem())
                   .Add(new UserMoveInputSystem());

            Systems.AddFeature(new DialogFeature(World, _analyticsHelper));
            
            //hooks
            Systems
                .AddFeature(new HealthDepletionEffectFeature(World))
                .AddFeature(new StaminaDepletionEffectFeature(_staminaView))
                .AddFeature(new ShieldDepletionEffectFeature(_shieldBarView));

                // ai.
            Systems
                .Add(new AiMoveSystem())
                .Add(new UnitAiThrowSystem())
                .Add(new AiChargedThrowAccuracyDeactivator())
                .Add(new UnitAiChargeThrowSystem())
                .Add(new DisconnectedAiSystem())
                .Add(new ReconnectAiSystem())
                ;

            Systems
                .AddFeature(new StaminaUsageSkipEffectFeature(_staminaView))
                .AddFeature(new StaminaFeature(_staminaView, _staminaCooldownView));
            
            Systems
                .AddFeature(new ShurikenFeature());
            
            if (featureConfig.ChargedThrow)
            {
                Systems
                    .AddFeature(new ChargedThrowFeature(_hudView, featureConfig))
                    ;
            }
            
            if (featureConfig.Ftue)
            {
                Systems
                    .Add(new FtueCancelHitToUserSystem());
            }
            
            Systems
                // game events. second cycle.
                .Add(new HitSystem())
                .Add(new DamageNodeSafeSystem())
                .Add(new IsDamagedSystem())
                .Add(new IsStaminaAvailableSystem())
                .Add(new HealthCompareSystem())
                .Add(new ShieldCompareSystem())
                .Add(new AiShieldActivationSystem())
                .Add(new AiShieldDeactivationSystem())
                .Add(new HasWoundSystem())
                .Add(new IsConnectedSystem());
            
            if (featureConfig.AdaptiveAi)
            {
                Systems.Add(new AiAdaptationByPlayerHpSystem());
            }

            // abilities.
            if (featureConfig.Abilities)
            {
                Systems.Add(new ShurikenAbilityCreateSystem())
                       .Add(new AmuletAbilityCreateSystem())
                    ;
            }

            Systems
                .Add(new EffectByItemActivationSystem())
                .Add(new EffectByTimerActivationSystem<FpvEffectMarker>())
                .Add(new AiBottlesActivationSystem())
                .Add(new AiEffectDeactivationByTimerSystem())
                .Add(new EffectDurationSystem())
                .Add(new EffectDeactivationByTimerSystem())
                .Add(new EffectDeactivationByDurationSystem())
                .Add(new EffectDeactivationByTargetDeletionSystem())

                .AddFeature(new InfiniteStaminaEffectFeature(_staminaView))
                
                .AddFeature(new ChangeThrowCostEffectFeature(_staminaView))

                .AddFeature(new AiChangeShieldUsageEffectFeature())
                .AddFeature(new AiChangeThrowAccuracyEffectFeature())
                .AddFeature(new AiChangeThrowFrequencyEffectFeature())

                .AddFeature(new ChangeStaminaRecoverySpeedEffectFeature(_staminaView))
                .AddFeature(new AutoHealEffectFeature(_healthView))
                

                // bleeding
                .Add(new AbilityCooldownSystem<BleedingBattleAbility>())
                .Add(new BleedingBattleAbilitySystem())
                .Add(new BattleAbilitySfxSystem<BleedingBattleAbility>())
                .Add(new BattleAbilityUsageCounterSystem<BleedingBattleAbility>())

                .Add(new BleedingModificatorEffectSystem())

                .AddFeature(new BleedingEffectFeature())
                .AddFeature(new FreezeEffectFeature(_overlayCanvas.transform))
                .AddFeature(new SpeedupMovementEffectFeature())

                // berserk
                .Add(new BattleAbilityModifyByItemSystem<BerserkBattleAbility>())
                .Add(new BerserkBattleAbilitySystem())
                .Add(new BerserkBattleAbilityVisualSystem())
                .AddFeature(new BattleAbilityFeature<BerserkBattleAbility>(_effectUiBundle))
                // vampirism
                .Add(new BattleAbilityModifyByItemSystem<VampirismBattleAbility>())
                .Add(new VampirismBattleAbilitySystem())
                .Add(new VampirismBattleAbilityVisualSystem())
                .AddFeature(new BattleAbilityFeature<VampirismBattleAbility>(_effectUiBundle))
                // invulnerability
                .Add(new InvulnerabilityBattleAbilitySystem())
                .Add(new InvulnerabilityBattleAbilityVisualSystem())
                .AddFeature(new BattleAbilityFeature<InvulnerabilityBattleAbility>(_effectUiBundle))
                // deflection
                .Add(new DeflectionBattleAbilitySystem())
                .Add(new DeflectionBattleAbilityVisualSystem())
                .AddFeature(new BattleAbilityFeature<DeflectionBattleAbility>(_effectUiBundle))
                // evade
                .Add(new EvadeBattleAbilitySystem())
                .Add(new EvadeBattleAbilityVisualSystem())
                .AddFeature(new BattleAbilityFeature<EvadeBattleAbility>(_effectUiBundle))

                .AddFeature(new ChangeDamageAmountFeature())
                .AddFeature(new ShieldPiercingDamageEffectFeature())
                .AddFeature(new ChargedThrowAbilityFeature(_trailVfxPoolRoot, _hudView))
                ;

            Systems
                .Add(new EffectIconSystem(_effectUiBundle))
                ;
            
#if DEBUG_PANEL
            Systems
                .Add(new DebugStaminaOverrideSystem());
#endif

            Systems
                // unit shield.
                .AddFeature(new ShieldFeature(featureConfig, World, _overlayCanvas, _shieldBarView, _googleDocsData, _botDifficultyProvider))
                // user hand shuriken.
                .Add(new UserHandShurikenInsufficientStaminaSystem())
                .Add(new UserHandShurikenMoveSystem())
                .Add(new UserHandShurikenLoadSystem())
                .Add(new UserHandShurikenRotationSystem())
                .Add(new UserThrowGestureTrailSystem())
                // unit moving.
                .AddFeature(new UnitMovementFeature())
                .Add(new BattleCameraSystem(_unitCamera.Data))
                ;
                // .Add(new UnitDirectionSystem());
            
            if (!featureConfig.Offline)
            {
                Systems
                    .Add(new FtueFirstTraumaSystem());
            }

            if (featureConfig.Traumas)
            {
                Systems.AddFeature(new TraumaFeature(_ftueProgress));
            }
#if DEBUG_PANEL
            Systems.Add(new UnitInvincibilitySystem());
#endif
                // fx.
            Systems
                .Add(new UnitTintSystem())
                .Add(new ShurikenImpactVfxSystem())
                .Add(new ShurikenImpactSfxSystem())
                // ui.
                .Add(new DamageVisualSystem())
                // game events.
                .Add(new BattleAnalyticsTrackSystem())
                .Add(new UnitDeathSystem())
                .Add(new UnitDeathSfxSystem())
                .OneFrame<BattleEnded>()
                .Add(new EndingSystem(_hudVisibilityController))
                // durability
                .Add(new DurabilityDamageOutfitSystem())
                .Add(new DurabilityDamageShurikenSkin())
                // damage
                .Add(new UnitDamageFeedbackSystem(_readyFightView))
                .Add(new UnitDamageApplySystem())
                // disconnect
                .Add(new ConnectionStateViewSystem())
                .Add(new DisconnectedEnemySystem())
                .Add(new DisconnectedUnitMarkerSystem())
                .Add(new DisconnectedUnitViewSystem())
                .Add(new DisconnectedEnemyTimerViewSystem())
                ;

            //QTE
            if (featureConfig.Qte)
            {
                Systems
                    .AddFeature(new QteFeature(_qteMainHUDView, _botDifficultyProvider));
            }

            // battle result
            Systems
                .Add(new BattleStatsCacheSystem())
                .Add(new BattleResultStatisticsSystem())
                .Add(new VictoryDefeatPrepareSystem(featureConfig))
                .Add(new VictoryDefeatSystem(featureConfig.Ftue))
                .Add(new BattleResultTransitionSystem(featureConfig, _overlayCanvas.transform))
                .Add(new ResourcesTopPanelRefreshCurrencySystem(false));

            if (!featureConfig.Offline)
            {
                Systems
                    .Add(new RemoveHpChangesAfterBattle())
                    .Add(new BattleResultServerRequestSystem())
                    .Add(new BattleResultAnalyticsTrackSystem(featureConfig))
                    .Add(new LoseBattleAnalyticsSystem())
                    .OneFrame<PlayerLostItemsEvent>();
            }

            Systems
                .Add(new BattleTimeLifeCycleSystem())
                .Observe<BattleEnded>()
                // ui.
                .Add(new UnitMoveUiSystem())
                .Add(new EnemyHealthBarSystem())
                .Add(new PlayerHealthBarViewSystem(_healthView))
                .Add(new TraumaEffectIconSystem(_effectUiBundle))
                // quests
                .AddFeature(new HintShieldFeature())
                .Add(new AudioSystem())
                .Add(new FadeOutSystem())
                .Add(new EffectCleanupSystem())
                // one-frames.
                .Observe<BattleStarted>()
                .OneFrame<BattleStarted>()
                .OneFrame<UserHandShurikenMoveEvent>()
                .OneFrame<UserHandShurikenResetMoveEvent>()
                .OneFrame<UserThrowGesture>()
                .InjectFromContainer(_lifeTimeScope.Container)
                ;
        }

#if UNITY_EDITOR
        void OnDrawGizmos()
        {
            _navMesh?.DrawDebugInfo();
        }
#endif
    }
}