using Client.Common.AI.Data;
using Client.Common.AI.Givers;
using Client.Common.Battle.WinRate;
using Client.Common.CSV;
using Client.Common.Player.Controllers;
using Client.TopDown.Region.AI.Systems;
using Client.TopDown.Region.Components;
using Client.TopDown.Region.Data;
using Client.TopDown.Region.NinjaHidingSpot.Systems;
using Client.TopDown.Region.Services;
using Client.TopDown.SpawnSystem.Creators;
using Client.TopDown.SpawnSystem.Systems.Additional;
using Client.TopDown.SpawnSystem.Systems.AmuletProviders;
using Client.TopDown.SpawnSystem.Systems.BombProviders;
using Client.TopDown.SpawnSystem.Systems.GuidProviders;
using Client.TopDown.SpawnSystem.Systems.HookProviders;
using Client.TopDown.SpawnSystem.Systems.PowerStoneProviders;
using Client.TopDown.SpawnSystem.Systems.RankProviders;
using Client.TopDown.SpawnSystem.Systems.ShurikenProviders;
using Client.TopDown.SpawnSystem.Systems.SkinProviders;
using Client.TopDown.SpawnSystem.Systems.SpawnPointProviders;
using Client.TopDown.SpawnSystem.Systems.SpawnRequestCreators;
using Client.TopDown.SpawnSystem.Systems.TraumaProviders;
using Client.TopDown.SpawnSystem.Systems.WeightModifiers;
using Client.Utils.Extensions.EcsExtensions;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Leopotam.Ecs;

namespace Client.TopDown.SpawnSystem.Systems
{
    internal class BotSpawnFeature : IEcsFeature
    {
        private readonly GoogleDocsData _googleDocsData;
        private readonly IBotDifficultyProvider _botData;
        private readonly PlayerWinStreak _playerWinStreak;
        private readonly int _winRate;
        private readonly WinRateBotsGrades _winRateBot;

        public BotSpawnFeature(GoogleDocsData googleDocsData, IBotDifficultyProvider botData, PlayerWinStreak playerWinStreak, int winRate, WinRateBotsGrades winRateBot)
        {
            _googleDocsData = googleDocsData;
            _botData = botData;
            _playerWinStreak = playerWinStreak;
            _winRate = winRate;
            _winRateBot = winRateBot;
        }
        
        public void Build(EcsSystems systems)
        {
            RegionUnitSpawnSupport regionUnitSpawnSupport = new();
            SpawnCreator spawnCreator = new(_googleDocsData, _botData);

            systems
                .Add(new RegionUnitSpawnSupportInitSystem(regionUnitSpawnSupport))
                .Add(new NinjaPointsRandomSelectSystem(_googleDocsData))
                .Add(new AiRespawnByDistanceSystem())
                
                //Weight modifiers
                .Add(new WinStreakWeightModifierSystem(BattleAIType.Smurf, ModificationType.Increase, _playerWinStreak))
                
                // Spawn requesters
                .Add(new SpawnRequestCreatorByTimerSystem(regionUnitSpawnSupport))
                .Add(new NinjaToHidingSpotBindSystem())
                .Add(new SpawnRequestCreatorNinjaSystem(regionUnitSpawnSupport))
                
                // Rank providers
                .Add(new RankProviderSystem(_winRate))
                
                // Point providers
                .Add(new LooterPointProviderSystem())
                .Add(new LockExitForSpawnSystem())
                .Add(new ExitPointProviderSystem())
                .Add(new RandomPointProviderSystem())
                .Add(new NinjaPointProviderSystem())
                
                // Item providers
                .Add(new AvailableShurikenProviderSystem(_botData))
                .Add(new AvailableAmuletProviderSystem(_botData))
                .Add(new AvailableSkinProviderSystem())
                .Add(new PowerStoneProviderSystem(_botData))
                .Add(new BombPocketProviderSystem(_botData))
                .Add(new HookPocketProviderSystem(_botData))
                
                // Trauma providers
                .Add(new RandomTraumaProviderSystem(_winRate))
                
                // Guid providers
                .Add(new GuidProviderNinjaSystem())
                
                .Add(new SpawnRequestVerifierSystem())
                .Add(new BotSpawnCreatorSystem())
                .Add(new NinjaSpawnGuid())
                
                .Add(new LooterCorpseSpawnSystem())
                
                .Inject(regionUnitSpawnSupport)
                .Inject(spawnCreator)
                ;
        }
    }
}