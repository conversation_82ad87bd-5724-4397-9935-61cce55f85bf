using Client.Common;
using Client.Common.CSV;
using Client.Common.Ranks;
using UnityEngine;

namespace Client.TopDown.Region.Services
{
    public class BotMmrData
    {
        private readonly int _mmrOffsetMin;
        private readonly int _mmrOffsetMax;
        private readonly RanksInfoProvider _ranksInfoProvider;

        public BotMmrData(RanksInfoProvider ranksInfoProvider, GoogleDocsData googleDocsData)
        {
            _ranksInfoProvider = ranksInfoProvider;
            _mmrOffsetMin = googleDocsData.GetIntNumber("bot.mmr.offset.min", 1000);
            _mmrOffsetMin = googleDocsData.GetIntNumber("bot.mmr.offset.max", 2500);
        }
        
        public int GetBotMmr(int rank, int playerMmr)
        {
            if (rank >= Idents.Ranks.RANKS_COUNT - 1)
            {
                return Random.Range(playerMmr -_mmrOffsetMin, playerMmr + _mmrOffsetMax);
            }
            
            _ranksInfoProvider.TryGetRankInfo(rank, out RankInfo rankInfo);
            int points = Random.Range(rankInfo.PointsRangeMin, rankInfo.PointsRangeMax); 
            return Mathf.Clamp(points, 0, points);
        }
    }
}