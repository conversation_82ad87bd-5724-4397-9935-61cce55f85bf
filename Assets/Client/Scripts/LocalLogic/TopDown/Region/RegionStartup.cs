using System.Threading;
using System.Threading.Tasks;
using Client.Common.AI.BehaviourTree.Runtime.NodeSystems;
using Client.Common.AI.BehaviourTree.Runtime.Scripts.Systems;
using Client.Common.AI.Data;
using Client.Common.Analytics.Helpers;
using Client.Common.Audio.Banks;
using Client.Common.Battle.WinRate;
using Client.Common.BloodColba;
using Client.Common.Components.Unit;
using Client.Common.Configs.Components.Effects;
using Client.Common.Configs.Components.Items.TypeMarkers;
using Client.Common.CSV;
using Client.Common.Dialogs.Runtime.Systems;
using Client.Common.Durability;
using Client.Common.ECS.LocalWorld;
using Client.Common.Effects.Activation;
using Client.Common.Effects.Deactivation;
using Client.Common.Effects.Lifecycle;
using Client.Common.Ftue.HUD;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Postprocessing;
using Client.Common.ScreenTransitions;
using Client.Common.Traumas.Data;
using Client.Common.UI.Rewards.Core;
using Client.TopDown.Abilities.Systems;
using Client.TopDown.Common.Network.DisconnectedUnit;
using Client.TopDown.Common.SpeechTooltip.Controllers;
using Client.TopDown.Common.SpeechTooltip.Systems;
using Client.TopDown.Cutscene.Controllers;
using Client.TopDown.Region.AI.Controllers;
using Client.TopDown.Region.AI.Systems;
using Client.TopDown.Region.AI.Systems.DecisionSystem;
using Client.TopDown.Region.Components;
using Client.TopDown.Region.Components.UI;
using Client.TopDown.Region.Corpses;
using Client.TopDown.Region.Data;
using Client.TopDown.Region.Durability;
using Client.TopDown.Region.Looting;
using Client.TopDown.Region.Quests;
using Client.TopDown.Region.Services;
using Client.TopDown.Region.Systems;
using Client.TopDown.Region.Systems.Analytics;
using Client.TopDown.Region.Systems.Ftue;
using Client.TopDown.Region.Systems.UI;
using Client.TopDown.Region.Tooltips.Systems;
using Client.TopDown.Region.Treasures;
using Client.TopDown.Region.UnityComponents;
using Client.TopDown.Region.VisibilityBehaviour.Cover;
using Client.TopDown.Region.VisibilityBehaviour.General.Systems;
using Client.TopDown.Region.VisibilityBehaviour.Overlap;
using Client.TopDown.SpawnSystem.Components;
using Client.TopDown.SpawnSystem.Systems;
using Client.Utils.Extensions.EcsExtensions.SystemExtensions;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.Serialization;
using FtuePart02System = Client.TopDown.Region.Systems.Ftue.FtuePart02System;
using FtuePart04System = Client.TopDown.Region.Systems.Ftue.FtuePart04System;
using Client.Common.Network.Connection.ConnectionStatus;
using Client.Common.Network.Connection.Disconnect;
using Client.TopDown.Common;
using Client.TopDown.Region.DebugTools;
using Client.TopDown.Region.Effects.Implementations.BlockGear;
using Client.TopDown.Region.Effects.Implementations.BlockTopDownMotion;
using Client.TopDown.Region.Effects.Implementations.ChangeMovementSpeed;
using Client.TopDown.Region.Effects.Implementations.HookParry;
using Client.TopDown.Region.Effects.Implementations.RegionCooldown;
using Client.TopDown.Region.Gear;
using Client.Utils.Extensions;
using Client.TopDown.Region.Gear.Bombs.Components.Unit;
using Client.TopDown.Region.Gear.Bombs.EffectActivation;
using Client.TopDown.Region.Gear.Bombs.Systems;
using Client.TopDown.Region.Gear.Hooks;
using Client.TopDown.Region.Helpers;
using Client.TopDown.Region.Loadout.Systems;
using Client.TopDown.Region.UnitHeadNotification.Systems;
using Client.Utils.ResultTool.Results;
using VContainer;

namespace Client.TopDown.Region
{
    internal struct RegionScope
    {
    }

    internal sealed class RegionStartup : LocalWorld<RegionScope>
    {
        [SerializeField] private RegionLifeTimeScope _lifeTimeScope;
        [SerializeField] private RegionVisualData _visualData;
        [SerializeField] private RegionAudio _regionAudio;

        [FormerlySerializedAs("_prostprocessAA")]
        [SerializeField]
        PostprocessAA _postprocessAA;

        [Header("Speech")]
        [SerializeField] private Transform _speechRoot;

        [SerializeField] private RectTransform _mainCanvas;

        [Header("BloodColba")]
        [SerializeField] private BloodColbaView _bloodColbaView;

        [Header("Cover")]
        [SerializeField] private CoverEyeView _coverEyeView;

        [SerializeField] private RectTransform _coverEyeCanvasRect;

        [Header("NearestExit")]
        [SerializeField] private NearestExitView _nearestExitView;

        [Header("LocationName")]
        [SerializeField] private LocationNameView _locationNameView;

        [Inject] private NavMeshSupport _navMesh;
        [Inject] private CameraSupport _camera;
        [Inject] private PlayerManager _playerManager;
        [Inject] private MetaNet _metaNet;
        [Inject] private KillInfoResponseToKillsPerLocation _responseToKillsInfoPerLocationConverter;
        [Inject] private LocalSharedState _localSharedState;
        [Inject] private BotItemsData _botItemsData;
        [Inject] private GoogleDocsData _googleDocsData;
        [Inject] private IBotDifficultyProvider _botDifficultyProvider;
        [Inject] private CorpseFeature _corpseFeature;
        [Inject] private DurabilityService _durabilityService;
        [Inject] private RegionSupport _regionSupport;
        [Inject] private PlayerWinStreak _playerWinStreak;
        [Inject] private LocalAnalyticsHelper _analyticsHelper;
        [Inject] private SpeechHelper _speechHelper;
        
        private int _regionId;

        protected override async Task OnStart(CancellationToken cancellationToken)
        {
            _regionId = _playerManager.Session.RegionId;
            if (_regionId != 0)
            {
                // FIXME: should we cache it to not call backend each time?
                Result<ClientKillLocalInfoResponse> locationsKillsProgress = await _metaNet.GetLocationsKillsProgress(cancellationToken);
                _localSharedState.KillsProgress = _responseToKillsInfoPerLocationConverter.Convert(locationsKillsProgress.Value, _regionId);
            }     
            
            _botItemsData.InitPossibleItemsLists();

            WinRateHelper winRateHelper = new(_playerManager.Session.Progress, _googleDocsData);
            int winRate = winRateHelper.GetWinRateType();
            WinRateBotsGrades winRateBot = winRateHelper.GetWinRateBotType();

            UniTask<Result>[] tasks =
            {
                TryEquipDurabilityDefaultItems(_metaNet, _playerManager, cancellationToken, _durabilityService),
                _botDifficultyProvider.Init(winRateBot, cancellationToken)
            };
            Result[] results = await UniTask.WhenAll(tasks);

            if (results.AnyFailure())
            {
                return;
            }

            _playerManager.Session.BattleResult.Reset();

            Systems
                .AddFeature(new RegionQuestFeature())
                .Add(new InitialVersionSetupSystem())
                .Add(new UiCutsceneSystem())
                .Add(new VisualFxSystem())
                .Add(new EnvironmentInitSystem())
                .Add(new UserFixReturnFromBattlePositionSystem())
                .OneFrame<UnitSpawnEventHelper>()
                .Add(new UnitInitSystem())
                .Add(new UnitResetIdleViewSystem())
                .Add(new UserInputSystem())
                .AddFeature(new BehaviourTreeFeature(_botDifficultyProvider))
                .Add(new NewRegionAiBehaviourTreeSystem())
                .Add(new EvadeLockUpdateSystem())
                .Add(new AiTargetSearchSystem())
                .Add(new AiLootSourceSearchSystem())
                .Add(new AiMoveCloserWaypointSystem())
                .Add(new AiEvadeWaypointSystem())
                .Add(new AiRegionEvadeWaypointSystem())
                .Add(new AiRegionWanderWaypointSystem())
                .Add(new AiExitWaypointSystem())
                .Add(new AiMoveToLootSourceWaypointSystem())
                .Add(new AiMoveSystem())
                .Add(new AiIsTargetInZoneSystem())
                .Add(new AiIsTargetVisibleSystem())
                .Add(new IsHookReachableSystem())
                .Add(new AiThrowHookSystem())
                .Add(new AiThrowBombSystem())
                .Add(new AiIsUnderBombSystem())
                .Add(new AiRespawnByDistanceSystem())
                .Add(new DisconnectedAiSystem())
                .Add(new UnitInputSystem())
                .AddFeature(new BombFeature())
                .Add(new BombEffectActivationSystem())
                .Add(new CrowBombEvadeSystem())
#if DEBUG
                .Add(new PlayerUnitDebugSystem())
#endif

                .AddFeature(new HookFeature())
                .Add(new UnitTeleportationSystem(RegionExitPoint.RADIUS))
                .Add(new UnitExitFromRegionSystem())
                .Add(new TeleportInteractViewSystem(GetEnterDelay(_playerManager, _googleDocsData)))
                .Add(new UserTimersViewRegionSystem())
                .Add(new RegionAbilityCreateSystem())
                .Add(new EffectByTimerActivationSystem<TopDownEffectMarker>())
                .Add(new EffectDurationSystem())
                .Add(new EffectDeactivationByTimerSystem())
                .Add(new EffectDeactivationByDurationSystem())
                .Add(new EffectDeactivationByEmptyTargetSystem())
                .Add(new HookEvadeAbilitySystem())
                .AddFeature(new HookParryEffectFeature())
                .AddFeature(new ChangeMovementSpeedEffectFeature())
                .AddFeature(new BlockTopDownMotionEffectFeature())
                .AddFeature(new BlockGearEffectFeature())
                .AddFeature(new RegionCooldownEffectFeature())
                .Add(new UnitMoveSystem())
                .Add(new UserReviveFxSystem())
                .Add(new LocationSystem())
                .Add(new CameraSystem())
                .Add(new CameraShakeSystem())
                .Add(new UnitFootstepSystem())
                .Add(new DuelBeginSfxSystem())
                .Add(new DuelBeginSystem())
                .Add(new LocationNameSystem(_locationNameView))
                .Add(new FtueTargetSystem())
                .Add(new UiInteractionSystem())
                .Add(new CrowSystem())

                // abilities
                .Add(new CoverDistanceAbilitySystem())
                .Add(new MovementSpeedAbilitySystem())
                .Add(new MovementSpeedAbilityVisualSystem())

                // HUD
                .Add(new PlayerCountViewSystem())
                .Add(new ConnectionStateViewSystem())
                .Add(new UserGearIconSystem<BombItemMarker>(GearType.Bomb))
                .Add(new UserGearIconSystem<HookItemMarker>(GearType.Hook))
                .OneFrameEntity<PlayerCooldownVisual<BombItemMarker>>()
                .OneFrameEntity<PlayerCooldownVisual<HookItemMarker>>()
                ;

            if (!_regionSupport.CurrentRegionIsFtueRegion())
            {
                Systems
                    .AddFeature(new BotSpawnFeature(_googleDocsData, _botDifficultyProvider, _playerWinStreak, winRate, winRateBot))

                    // reconnect
                    .Add(new DisconnectedEnemySystem())
                    .Add(new DisconnectedUnitViewSystem())
                    .Add(new DisconnectedUnitMarkerSystem())
                    .Add(new ReconnectUnitSystem())

                    // NearExit
                    .Add(new NearestExitSystem(_nearestExitView));
                ;
            }

            if (_regionSupport.CurrentRegionIsFtueRegion())
            {
                Systems
                    .Add(new FtueHUDStateSystem())
                    .Add(new FtuePart01KarasDialogSystem())
                    .Add(new FtuePart01MoveToEnemySystem())
                    .Add(new FtuePart02System(_mainCanvas))
                    .Add(new FtuePart03System(_mainCanvas))
                    .Add(new FtuePart04System(_mainCanvas))
                    .Add(new FtueKarasRankUnlockSystem());
            }

            Systems
                .Add(new FtueFirstEnterToRegionSystem())
                .Add(new FtueCheckFullBloodColba())
                .Add(new FtueTraumaRegionHintSystem())
                .Add(new MarketClearImportantTooltipsSystem())
                //after ftue
                .Add(new ProgressButtonControlSystem())
                .Add(new UserInputViewUpdateSystem())
                .Add(new LeaveRegionSystem())
                .AddFeature(new DialogFeature(World, _analyticsHelper))
                .Add(new UnitSpeechSystem(_mainCanvas, _speechRoot))
                .Add(new CorpseBloodSpeechSystem())

                .AddFeature(new BloodColbaFeature(_bloodColbaView, _playerManager.Session))
                .AddFeature(_corpseFeature)

                .AddFeature(new LootFeature())
                .AddFeature(new TreasureFeature())
                .AddFeature(new TooltipsFeature(_mainCanvas))
                .AddFeature(new UnitHeadNotificationFeature())
                
                //consumables
                .Add(new PocketItemsRegionSpendSystem())
                .Add(new SpendPocketItemsSystem())
                // loadout
                .Add(new RefreshLoadoutByPocketSystem())
                .OneFrameEntity<SpendPocketItem>()
                
                // should be last.
                .Add(new AudioSystem())
                .Add(new RegionAnalyticsTrackerSystem())
                .Add(new PlayerShurikenPropertiesUpdateSystem())
                .Add(new VisibilityUpdateSystem())
                .AddFeature(new OverlapVisibilityFeature())
                .AddFeature(new CoverFeature(_coverEyeView, _coverEyeCanvasRect))
                .Add(new EffectCleanupSystem())
                .Add(new BombKillSystem())
                .Add(new FadeOutSystem())
                .OneFrame<UnitEnteredBombRange>()
                .OneFrame<UnitExitedBombRange>()
                .OneFrame<UnitInput>()
                .OneFrame<UnitTeleportedEvent>()
                .OneFrame<LocationChangedEvent>()
                .InjectFromContainer(_lifeTimeScope.Container)
                ;
        }

        private static float GetEnterDelay(PlayerManager playerManager, GoogleDocsData googleDocsData)
        {
            const string defaultEnterDelayKey = "region.exit.delay";
            const string ftueEnterDelayKey = "region.exit.ftue-delay";

            string enterDelayKey = playerManager.Session.RegionId == 0 ? ftueEnterDelayKey : defaultEnterDelayKey;

            return googleDocsData.GetNumber(enterDelayKey);
        }

        private async UniTask<Result> TryEquipDurabilityDefaultItems(MetaNet metaNet, PlayerManager playerManager, CancellationToken cancellationToken, DurabilityService durabilityService)
        {
            DurabilityDefaultItemsEquipper durabilityEquipper = new(
                World,
                metaNet,
                playerManager,
                durabilityService
            );

            Result outfitResult = await durabilityEquipper.TryEquipDefaultOutfit(cancellationToken);
            if (outfitResult.IsFailure)
            {
                return Result.Failure(outfitResult.Error);
            }

            Result skinResult = await durabilityEquipper.TryEquipDefaultShurikenSkin(cancellationToken);
            if (skinResult.IsFailure)
            {
                return Result.Failure(skinResult.Error);
            }

            return Result.Success();
        }
        
#if UNITY_EDITOR
        void OnDrawGizmos()
        {
            if (Application.isPlaying)
            {
                Rect rect = _navMesh.GetBounds();
                Vector3 center = rect.center;
                center.z = center.y;
                center.y = 0f;

                Vector3 size = rect.size;
                size.z = size.y;
                size.y = 0f;

                Gizmos.color = Color.red;
                Gizmos.DrawWireCube(rect.center, size);

                center = (_camera.MaxBound + _camera.MinBound) * 0.5f;
                center.z = center.y;
                center.y = 0f;

                size = _camera.MaxBound - _camera.MinBound;
                size.z = size.y;
                size.y = 0f;
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireCube(center, size);
            }
        }
#endif
    }
}