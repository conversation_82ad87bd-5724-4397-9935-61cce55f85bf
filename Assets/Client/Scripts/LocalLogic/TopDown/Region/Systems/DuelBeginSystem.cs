using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Client.Common;
using Client.Common.AI;
using Client.Common.AI.Data;
using Client.Common.AI.Givers;
using Client.Common.Analytics.Infrastructure.Events;
using Client.Common.Audio.Banks;
using Client.Common.BlurTool.ScreenshotBlur;
using Client.Common.Components.Unit;
using Client.Common.Configs.Components.Effects;
using Client.Common.Countries;
using Client.Common.CSV;
using Client.Common.Ftue.Helpers;
using Client.Common.Ftue.Infrastructure;
using Client.Common.GameSettings;
using Client.Common.LocalLogic.TopDown.Unit;
using Client.Common.Network.MetaNet;
using Client.Common.Player.Controllers;
using Client.Common.Player.PocketFeature.Pockets;
using Client.Common.PowerStoneFeature;
using Client.Common.Ranks;
using Client.Common.SceneLoading;
using Client.Common.SceneLoading.SceneData.Implementations;
using Client.Common.ScreenTransitions;
using Client.Common.ScreenTransitions.Views.VSScreen;
using Client.Common.TimeGiver.Abstractions;
using Client.TopDown.Region.Components;
using Client.TopDown.Region.Data;
using Client.TopDown.Region.Services;
using Client.Utils.CameraTools;
using Client.Utils.ECS.LocalWorld;
using Client.Utils.ResultTool.Results;
using Common;
using Leopotam.Ecs;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Client.TopDown.Region.Systems {
    sealed class DuelBeginSystem : IEcsInitSystem, IEcsRunSystem
    {
        private readonly LocalEcsWorld _world = default;
        private readonly PlayerManager _playerManager = default;
        private readonly TransitionService _transitionService = default;
        private readonly SceneLoader _sceneLoader = default;
        private readonly GoogleDocsData _googleDocsData = default;
        private readonly MetaNet _metaNet = default;
        private readonly CountryData _countryData = default;
        private readonly BotNameData _botNameData = default;
        private readonly BotAITypeGiver _botAITypeGiver = default;
        private readonly FtueProgress _ftueProgress = default;
        private readonly RegionSupport _regionSupport = default;
        private readonly BattleAudio _battleAudio = default;
        private readonly GameSettingsGiver _gameSettingsGiver = default;
        private readonly ITimeGiver _timeService = default;
        private readonly RanksInfoProvider _ranksInfoProvider = default;
        
        private readonly EcsFilter<DuelBeginEvent> _beginDuelEvent = default;
        private readonly EcsFilter<TopDownUnit>.Exclude<PlayerFlag> _bots = default;
        private readonly ScreenshotBlurService _screenshotBlur = default;

        private BattleAiTypeProvider _battleAiTypeProvider;
        private BotMmrData _botMmrData;
        
        public void Init()
        {
            _battleAiTypeProvider = new BattleAiTypeProvider(_regionSupport, _botAITypeGiver, _world);
            _botMmrData = new BotMmrData(_ranksInfoProvider, _googleDocsData);
        }
        
        public void Run()
        {
            foreach (int idx in _beginDuelEvent)
            {
                ref DuelBeginEvent beginDuel = ref _beginDuelEvent.Get1(idx);
                float t = beginDuel.FadeCooldown - Time.time;
                if (idx == 0)
                {
                    float cameraSize = Camera.main.orthographicSize;
                    EcsEntity opponent = beginDuel.Opponent;
                    ZoomCamera(cameraSize, t);
                    ref TopDownUnit unit = ref beginDuel.Player.Get<TopDownUnit>();
                    ref TopDownUnit rival = ref beginDuel.Opponent.Get<TopDownUnit>();
                    PocketsStorage pocketsStorage = opponent.Get<PocketsStorageComponent>().Storage;
                    Vector3 playerPos = unit.Position;
                    Vector3 playerDir = unit.Direction;
                    _playerManager.Session.RegionSessionData.ReturnFromBattlePosition = playerPos;
                    _playerManager.Session.RegionSessionData.ReturnFromBattleDirection = playerDir;
                    _playerManager.Session.RegionSessionData.EnteredFromMap = false;
                    bool playerWasHooked = beginDuel.PlayerWasHooked;
                    IReadOnlyList<EffectIdData> hookedUnitEffects = beginDuel.HookedUnitEffects;
                    IReadOnlyList<EffectIdData> hookerEffects = beginDuel.HookerEffects;

                    if (_playerManager.Session.RegionId > 0)
                    {
                        _playerManager.Session.BattleId = _playerManager.Session.LocationId - 1;
                    }

                    
                    BattleAITypeData rivalBattleAITypeData = _battleAiTypeProvider.GetBattleAi(rival.Rank);
                    int visualRank = GetAiVisualRank(rivalBattleAITypeData.BattleAIType, rival.Rank);
                    int botMmr = _botMmrData.GetBotMmr(visualRank, _playerManager.Session.Progress.RankPoints);
                    int rivalRank = GetBotRank(rival.Rank, rivalBattleAITypeData.BattleAIType);

                    Rival enemy = new()
                    {
                        RivalName = GetBotName(rival),
                        RivalCountry = GetBotCountry(),
                        RivalBattleRank = rivalRank,
                        RivalVisualRank = visualRank,
                        RivalSkin = rival.OutfitId,
                        RivalShuriken = rival.ShurikenId,
                        RivalShurikenSkin = rival.ShurikenSkinId,
                        RivalAmulet = rival.AmuletId,
                        RivalPowerStone  = new PowerStone(pocketsStorage.Get(PocketType.PowerStone).Item),
                        RivalHook  = beginDuel.OpponentHookId,
                        RivalTraumas = rival.TraumasId,
                        BattleAIType = rivalBattleAITypeData.BattleAIType
                    };

                    _screenshotBlur.Process(16,1);
                    if (!_regionSupport.CurrentRegionIsFtueRegion())
                    {
                        _transitionService.FadeIn(
                            new VSScreenTransitionView(_playerManager, _gameSettingsGiver, _googleDocsData, _world, _battleAudio, enemy, beginDuel.PlayerHookId), () =>
                            {
                                StartBattle(enemy, playerWasHooked, hookedUnitEffects, hookerEffects, botMmr);
                            });
                    }
                    else
                    {
                        _transitionService.FadeIn(
                            () =>
                            {
                                StartBattle(enemy, playerWasHooked, hookedUnitEffects, hookerEffects, botMmr);
                            });
                    }

                    _beginDuelEvent.GetEntity(idx).Destroy();
                }
            }
        }

        private static void ZoomCamera(float cameraSize, float t)
        {
            OrthoCameraZoom orthoCameraZoom = new GameObject("OrthoCameraZoom").AddComponent<OrthoCameraZoom>();
            orthoCameraZoom.Process(cameraSize, cameraSize * 0.3f, t, (c) => { });
        }

        private async void StartBattle(Rival enemy, bool playerWasHooked, IReadOnlyList<EffectIdData> hookedUnitEffects, IReadOnlyList<EffectIdData> hookerEffects, int botMmr)
        {
            BattleFeatureConfig battleConfig;
            if (_playerManager.Session.RegionId > 0)
            {
                await EnterBattle(enemy);
                battleConfig = BattleFeatureConfig.Default();
                if (!_ftueProgress.Conditions.IsTraumaFeatureEnabled())
                {
                    battleConfig.Traumas = false;
                }
                if (!_ftueProgress.Conditions.IsQteEnabled())
                {
                    battleConfig.Qte = false;
                }
                if (!_ftueProgress.Conditions.IsShieldParryEnabled())
                {
                    battleConfig.Parrying = false;
                }
                if (!_ftueProgress.Conditions.IsPowerStonesEnabled())
                {
                    battleConfig.ChargedThrow = false;
                }
            }
            else
            {
                battleConfig = BattleFeatureConfig.ForTutorialRegion();
            }
            
            _playerManager.Session.BattleResult.BattleStartTime = _timeService.UtcNow;

            _playerManager.Session.BattleResult.StartBattle(
                _playerManager.Session.AccountInfo.Nickname,
                _gameSettingsGiver.CountryState,
                enemy.RivalName,
                enemy.RivalCountry,
                enemy.RivalBattleRank,
                enemy.RivalVisualRank,
                enemy.RivalSkin,
                enemy.RivalShuriken,
                enemy.RivalShurikenSkin,
                enemy.RivalAmulet,
                enemy.RivalPowerStone,
                enemy.RivalHook,
                enemy.RivalTraumas,
                playerWasHooked, enemy.BattleAIType, 
                hookedUnitEffects, hookerEffects, botMmr);

            _playerManager.Session.RegionSessionData.BotData.BotCountOnEnterBattle = (uint) _bots.GetEntitiesCount();
            
            BattleSceneLoadData battleSceneLoadData = new(battleConfig);
            _sceneLoader.Load (battleSceneLoadData, SceneHistoryMode.ClearHistory);
        }

        private string GetBotName(TopDownUnit rival)
        {
            if (_regionSupport.RegionId == 0)
            {
                return FtueConditions.BOT_NAME_FOR_TUTORIAL_BATTLES;
            }
            
            return _botNameData.GetRandomName(rival.Rank, rival.OutfitId, rival.ShurikenSkinId);
        }

        private string GetBotCountry()
        {
            if (_regionSupport.RegionId == 0)
            {
                return FtueConditions.BOT_COUNTRY_FOR_TUTORIAL_BATTLES;
            }

            return _countryData.GetRandomNeighbor(_gameSettingsGiver.CountryState);
        }
        
        private int GetBotRank(int rivalRank, BattleAIType aiType)
        {
            return aiType == BattleAIType.Killer ? _googleDocsData.GetIntNumber("battle.bot.killer.rank", 14) : rivalRank;
        }
        
        private BattleOpponentType GetBattleAiType(BattleAIType battleAIType) => (BattleOpponentType) Enum.Parse(typeof(BattleOpponentType), battleAIType.ToString());

        private async Task EnterBattle (Rival enemy) {
            BattleOpponent battleOpponent = new()
            {
                Rank = (ushort)enemy.RivalBattleRank, Outfit = (ushort)enemy.RivalSkin,
                Country = enemy.RivalCountry, Shuriken = (ushort)enemy.RivalShuriken,
                Type = GetBattleAiType(enemy.BattleAIType),
                Points = (ushort)_playerManager.Session.BattleResult.EnemyMmr,
                Injuries = new BattleOpponentInjuries(),
                Damage = new BattleOpponentDamage()
            };
            
            Result<ClientBattleEnterResponse> enterBattle = await _metaNet.EnterBattle(battleOpponent);
            if (enterBattle.IsFailure) { return; }
            _playerManager.Session.BattleResultId = enterBattle.Value.Battle.Id;
            _playerManager.Session.LastBattleResultId = enterBattle.Value.Battle.Id;
        }

        private int GetAiVisualRank(BattleAIType battleAIType, int rankId)
        {
            if (battleAIType != BattleAIType.Killer)
            {
                return rankId;
            }

            int killerRank = Mathf.Clamp(_playerManager.Session.AccountInfo.Rank + Random.Range(0, 4), 0, Idents.Ranks.RANKS_COUNT - 1);
            return killerRank;
        }
    }
}
